#!/bin/bash

# Fail on errors
set -e

# Root module
echo "Generating docs for root module..."
terraform-docs markdown . > README.md

# Loop through each submodule in ./modules/
for module in ./modules/*; do
  if [ -d "$module" ]; then
    echo "Generating docs for module: $module"
    terraform-docs markdown "$module" > "$module/README.md"
  fi
done

echo "✅ Documentation generated for all modules."

